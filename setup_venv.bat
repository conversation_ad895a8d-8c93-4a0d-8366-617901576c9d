@echo off
setlocal enabledelayedexpansion

rem Change directory to the repo root (this script's directory)
pushd "%~dp0"

echo Creating virtual environment...
python -m venv .venv
if %ERRORLEVEL% neq 0 (
    echo Failed to create virtual environment
    popd
    exit /b 1
)

echo Activating virtual environment...
call .venv\Scripts\activate.bat
if %ERRORLEVEL% neq 0 (
    echo Failed to activate virtual environment
    popd
    exit /b 1
)

echo Installing requirements...
pip install -r requirements.txt
if %ERRORLEVEL% neq 0 (
    echo Failed to install requirements
    popd
    exit /b 1
)

echo.
echo Virtual environment setup complete!
echo To activate manually: .venv\Scripts\activate.bat
echo To start trading: start_trading.bat
echo.

popd
pause
