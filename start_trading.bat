@echo off
setlocal enabledelayedexpansion

rem Change directory to the repo root (this script's directory)
pushd "%~dp0"

rem Check if virtual environment exists
if not exist ".venv\Scripts\python.exe" (
    echo Virtual environment not found. Please run setup_venv.bat first.
    popd
    pause
    exit /b 1
)

rem Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat
if %ERRORLEVEL% neq 0 (
    echo Failed to activate virtual environment
    popd
    pause
    exit /b 1
)

echo Starting TraderBot live trading loop...
python -m src.traderbot.main
set EXITCODE=%ERRORLEVEL%

echo TraderBot exited with code %EXITCODE%
popd
exit /b %EXITCODE%

